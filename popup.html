<html>
    <head>
        <link rel="stylesheet" href="popup.css">
    </head>
    <body>
        <button id="nsu_shmeat" class="Shmeat" disabled=true>Shmeat</button>
        <button id="nsu_JSON" class="Scan" >Generate JSON</button>
        <input type="file" id="json_picker" name="json_picker" accept=".json">
        </br>
        Accuracy <input class="AccInput" id="acc_input" type="number" name="acc_input" min="1" max="100" value="100">%
        </br>
        Easy Delay Time <input class="DelayInput" id="easy_delay" type="number" name="easy_delay" min="1" max="600" value="60">
        Medium Delay Time <input class="DelayInput" id="med_delay" type="number" name="med_delay" min="1" max="600" value="90">
        Difficult Delay Time <input class="DelayInput" id="diff_delay" type="number" name="diff_delay" min="1" max="600" value="120">

        <script src="popup.js"></script>
    </body>
</html>
