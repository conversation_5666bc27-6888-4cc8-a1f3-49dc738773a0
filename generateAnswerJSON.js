// 新版本：适配单题练习模式
var resultsArray = [];
var currentQuestionIndex = 0;
var totalQuestions = 0;
var isCollecting = false;

// 获取总题目数
function getTotalQuestions() {
    var questionInfo = document.querySelector('[class*="question-navigator"]');
    if (questionInfo) {
        var match = questionInfo.textContent.match(/(\d+)\s*\/\s*(\d+)/);
        if (match) {
            currentQuestionIndex = parseInt(match[1]) - 1; // 转换为0基索引
            totalQuestions = parseInt(match[2]);
            console.log(`Found ${totalQuestions} total questions, currently on question ${currentQuestionIndex + 1}`);
            return true;
        }
    }
    return false;
}

// 获取当前题目的问题文本
function getCurrentQuestion() {
    // 尝试多种可能的选择器来获取题目文本
    var questionSelectors = [
        '.question-wrapper__body .markdown-renderer-v2',
        '.question-wrapper .markdown-renderer-v2',
        '[class*="question-wrapper"] [class*="markdown"]',
        '.question-wrapper__body p',
        '.question-wrapper p'
    ];

    for (let selector of questionSelectors) {
        var questionElement = document.querySelector(selector);
        if (questionElement && questionElement.textContent.trim()) {
            return questionElement.textContent.trim();
        }
    }

    // 如果找不到，返回一个基于当前题目编号的标识
    return `Question ${currentQuestionIndex + 1}`;
}

// 获取正确答案
function getCorrectAnswer() {
    // 查找正确答案标识
    var correctIndicators = document.querySelectorAll('[class*="correct"]');

    for (let indicator of correctIndicators) {
        var optionWrapper = indicator.closest('.mcq-option-accessible-wrapper');
        if (optionWrapper) {
            var letterElement = optionWrapper.querySelector('.mcq-option__letter');
            if (letterElement) {
                return letterElement.textContent.trim().toUpperCase();
            }
        }
    }

    // 如果找不到正确答案标识，可能题目还没有被回答
    console.warn("No correct answer found for current question");
    return null;
}

// 导航到指定题目
function navigateToQuestion(questionNum) {
    return new Promise((resolve) => {
        var navigatorWrapper = document.querySelector('.question-dropdown-navigator__wrapper');
        if (navigatorWrapper) {
            var dropdown = navigatorWrapper.querySelector('select, button');
            if (dropdown) {
                // 如果是下拉菜单，尝试选择对应选项
                if (dropdown.tagName === 'SELECT') {
                    dropdown.value = questionNum;
                    dropdown.dispatchEvent(new Event('change'));
                } else {
                    // 如果是按钮，点击它
                    dropdown.click();
                    setTimeout(() => {
                        // 查找题目选项
                        var options = document.querySelectorAll(`[data-question="${questionNum}"], option[value="${questionNum}"]`);
                        if (options.length > 0) {
                            options[0].click();
                        }
                    }, 1000);
                }
            }
        }

        // 等待页面加载
        setTimeout(resolve, 3000);
    });
}

// 收集当前题目的答案
function collectCurrentAnswer() {
    var question = getCurrentQuestion();
    var answer = getCorrectAnswer();

    if (question && answer) {
        var answerObj = {};
        answerObj[question] = answer;
        resultsArray.push(answerObj);
        console.log(`Collected: ${question} -> ${answer}`);
        return true;
    } else {
        console.warn(`Failed to collect answer for question ${currentQuestionIndex + 1}`);
        return false;
    }
}

// 主要收集函数
async function startCollection() {
    if (isCollecting) {
        console.log("Collection already in progress");
        return;
    }

    isCollecting = true;
    console.log("Starting answer collection...");

    if (!getTotalQuestions()) {
        alert("Could not determine total number of questions. Make sure you're on a practice page.");
        isCollecting = false;
        return;
    }

    // 从第一题开始收集
    for (let i = 0; i < totalQuestions; i++) {
        console.log(`Processing question ${i + 1} of ${totalQuestions}`);

        // 导航到题目
        await navigateToQuestion(i + 1);

        // 收集答案
        collectCurrentAnswer();

        // 短暂延迟
        await new Promise(resolve => setTimeout(resolve, 1000));
    }

    // 完成收集，下载文件
    createJSON();
    isCollecting = false;
}

function createJSON() {
    if (resultsArray.length === 0) {
        alert("No answers collected. Make sure the questions have been answered first.");
        return;
    }

    let jsonContent = "data:text/json;charset=utf-8," + JSON.stringify(resultsArray, null, 2);
    let encodedUri = encodeURI(jsonContent);
    let link = document.createElement("a");
    link.setAttribute("href", encodedUri);

    // 尝试获取练习名称
    var titleElement = document.querySelector('[class*="title"]');
    var fileName = titleElement ?
        titleElement.textContent.toLowerCase().replace(/[^a-z0-9]/g, "_") + ".json" :
        "albert_answers.json";

    link.setAttribute("download", fileName);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    console.log(`Downloaded ${resultsArray.length} answers to ${fileName}`);
    alert(`Successfully collected ${resultsArray.length} answers!`);
}

// 启动收集
startCollection();
